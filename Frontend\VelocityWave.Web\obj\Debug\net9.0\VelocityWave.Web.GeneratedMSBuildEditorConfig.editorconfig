is_global = true
build_property.TargetFramework = net9.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = VelocityWave.Web
build_property.RootNamespace = VelocityWave.Web
build_property.ProjectDir = C:\Users\<USER>\Desktop\My Ideas\vwplatform\Frontend\VelocityWave.Web\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 9.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = true
build_property.MSBuildProjectDirectory = C:\Users\<USER>\Desktop\My Ideas\vwplatform\Frontend\VelocityWave.Web
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/About.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWJvdXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/Account/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWNjb3VudFxJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/Account/Login.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWNjb3VudFxMb2dpbi5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/Account/Logout.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWNjb3VudFxMb2dvdXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/Account/Manage/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWNjb3VudFxNYW5hZ2VcSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/Account/Register.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWNjb3VudFxSZWdpc3Rlci5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/Account/Settings.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWNjb3VudFxTZXR0aW5ncy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/AddOns/Builder.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWRkT25zXEJ1aWxkZXIuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/AddOns/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWRkT25zXERldGFpbHMuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/AddOns/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWRkT25zXEluZGV4LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/AddOns/WorkflowBuilder.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWRkT25zXFdvcmtmbG93QnVpbGRlci5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/Admin/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWRtaW5cSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/Admin/Settings.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWRtaW5cU2V0dGluZ3MuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/Admin/Users.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWRtaW5cVXNlcnMuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/Analytics.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQW5hbHl0aWNzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/AuthTest.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQXV0aFRlc3QuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/Billing/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQmlsbGluZ1xJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/Contact.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQ29udGFjdC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/Dashboard/Addons.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcRGFzaGJvYXJkXEFkZG9ucy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/Dashboard/Analytics.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcRGFzaGJvYXJkXEFuYWx5dGljcy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/Dashboard/Debug.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcRGFzaGJvYXJkXERlYnVnLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/Dashboard/FixSubscription.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcRGFzaGJvYXJkXEZpeFN1YnNjcmlwdGlvbi5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/Dashboard/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcRGFzaGJvYXJkXEluZGV4LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/Dashboard/Websites.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcRGFzaGJvYXJkXFdlYnNpdGVzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/Error.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcRXJyb3IuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/MyWebsites.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcTXlXZWJzaXRlcy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/Preview.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcUHJldmlldy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/Pricing.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcUHJpY2luZy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/Privacy.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcUHJpdmFjeS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/Shared/_Layout.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU2hhcmVkXF9MYXlvdXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/Shared/_LoginPartial.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU2hhcmVkXF9Mb2dpblBhcnRpYWwuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/Shared/_ValidationScriptsPartial.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU2hhcmVkXF9WYWxpZGF0aW9uU2NyaXB0c1BhcnRpYWwuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/SiteBuilder/AddPage.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU2l0ZUJ1aWxkZXJcQWRkUGFnZS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/SiteBuilder/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU2l0ZUJ1aWxkZXJcSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/SiteBuilder/Minimal.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU2l0ZUJ1aWxkZXJcTWluaW1hbC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/SiteBuilder/Templates.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU2l0ZUJ1aWxkZXJcVGVtcGxhdGVzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/_ViewImports.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX1ZpZXdJbXBvcnRzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/My Ideas/vwplatform/Frontend/VelocityWave.Web/Pages/_ViewStart.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX1ZpZXdTdGFydC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 
