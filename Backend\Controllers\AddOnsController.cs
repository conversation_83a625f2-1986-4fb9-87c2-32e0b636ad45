using Backend.Data;
using Backend.Models;
using Backend.Models.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace Backend.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class AddOnsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<AddOnsController> _logger;

        public AddOnsController(
            ApplicationDbContext context,
            ILogger<AddOnsController> logger)
        {
            _context = context;
            _logger = logger;
        }

        // GET: api/addons
        [HttpGet]
        public async Task<ActionResult<IEnumerable<AddOnDto>>> GetAddOns()
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var isAdmin = User.IsInRole("Admin") || User.IsInRole("SuperAdmin");

            // <PERSON><PERSON> can see all add-ons, regular users can only see active ones
            var addOns = isAdmin
                ? await _context.AddOns.ToListAsync()
                : await _context.AddOns.Where(a => a.IsActive).ToListAsync();

            // Map to DTOs
            var addOnDtos = addOns.Select(a => new AddOnDto
            {
                Id = a.Id,
                Name = a.Name,
                Description = a.Description,
                Version = a.Version,
                Category = a.Category,
                Price = a.Price,
                Author = a.Author,
                IsActive = a.IsActive,
                CreatedAt = a.CreatedAt,
                UpdatedAt = a.UpdatedAt
            }).ToList();

            return Ok(addOnDtos);
        }

        // GET: api/addons/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<AddOnDto>> GetAddOn(Guid id)
        {
            var addOn = await _context.AddOns.FindAsync(id);

            if (addOn == null)
            {
                return NotFound();
            }

            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var isAdmin = User.IsInRole("Admin") || User.IsInRole("SuperAdmin");

            // Regular users can only see active add-ons
            if (!addOn.IsActive && !isAdmin)
            {
                return NotFound();
            }

            // Map to DTO
            var addOnDto = new AddOnDto
            {
                Id = addOn.Id,
                Name = addOn.Name,
                Description = addOn.Description,
                Version = addOn.Version,
                Category = addOn.Category,
                Price = addOn.Price,
                Author = addOn.Author,
                IsActive = addOn.IsActive,
                CreatedAt = addOn.CreatedAt,
                UpdatedAt = addOn.UpdatedAt
            };

            return Ok(addOnDto);
        }

        // POST: api/addons
        [HttpPost]
        public async Task<ActionResult<AddOnDto>> CreateAddOn(AddOnRequest request)
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized();
            }

            // Only admins can create add-ons
            var isAdmin = User.IsInRole("Admin") || User.IsInRole("SuperAdmin");
            if (!isAdmin)
            {
                // For MVP, we'll allow regular users to create add-ons too
                // In a production environment, you might want to restrict this
                _logger.LogWarning("Non-admin user {UserId} is creating an add-on", userId);
            }

            var addOn = new AddOn
            {
                Name = request.Name,
                Description = request.Description,
                Version = request.Version ?? "1.0.0",
                Category = request.Category,
                Price = request.Price,
                Author = request.Author ?? userId,
                IsActive = request.IsActive,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.AddOns.Add(addOn);
            await _context.SaveChangesAsync();

            // Map to DTO
            var addOnDto = new AddOnDto
            {
                Id = addOn.Id,
                Name = addOn.Name,
                Description = addOn.Description,
                Version = addOn.Version,
                Category = addOn.Category,
                Price = addOn.Price,
                Author = addOn.Author,
                IsActive = addOn.IsActive,
                CreatedAt = addOn.CreatedAt,
                UpdatedAt = addOn.UpdatedAt
            };

            return CreatedAtAction(nameof(GetAddOn), new { id = addOn.Id }, addOnDto);
        }

        // PUT: api/addons/{id}
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateAddOn(Guid id, AddOnRequest request)
        {
            var addOn = await _context.AddOns.FindAsync(id);

            if (addOn == null)
            {
                return NotFound();
            }

            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var isAdmin = User.IsInRole("Admin") || User.IsInRole("SuperAdmin");

            // Only admins can update add-ons
            if (!isAdmin)
            {
                return Forbid();
            }

            // Update add-on properties
            addOn.Name = request.Name;
            addOn.Description = request.Description;
            addOn.Version = request.Version ?? addOn.Version;
            addOn.Category = request.Category;
            addOn.Price = request.Price;
            addOn.Author = request.Author ?? addOn.Author;
            addOn.IsActive = request.IsActive;
            addOn.UpdatedAt = DateTime.UtcNow;

            _context.Entry(addOn).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!AddOnExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // DELETE: api/addons/{id}
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteAddOn(Guid id)
        {
            var addOn = await _context.AddOns.FindAsync(id);

            if (addOn == null)
            {
                return NotFound();
            }

            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var isAdmin = User.IsInRole("Admin") || User.IsInRole("SuperAdmin");

            // Only admins can delete add-ons
            if (!isAdmin)
            {
                return Forbid();
            }

            _context.AddOns.Remove(addOn);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool AddOnExists(Guid id)
        {
            return _context.AddOns.Any(e => e.Id == id);
        }
    }

    public class AddOnRequest
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string? Version { get; set; }
        public string Category { get; set; } = string.Empty;
        public decimal Price { get; set; } = 0;
        public string? Author { get; set; }
        public bool IsActive { get; set; } = true;
    }

    public class AddOnDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public decimal Price { get; set; }
        public string Author { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}
